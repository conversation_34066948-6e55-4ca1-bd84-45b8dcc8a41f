#!/usr/bin/env python3
"""
YouTube Data API v3 helper for transcript extraction.
This is the official, reliable way to access YouTube data.
"""

import logging
from typing import Optional, List, Dict
import requests
import re
from youtube_transcript_api import YouTubeTranscriptApi
from youtube_transcript_api._errors import TranscriptsDisabled, VideoUnavailable

logger = logging.getLogger(__name__)

class YouTubeAPIHelper:
    """Helper class using YouTube Data API v3 for reliable access."""
    
    def __init__(self, api_key: Optional[str] = None):
        self.api_key = api_key
        self.base_url = "https://www.googleapis.com/youtube/v3"
        
    def get_video_info(self, video_id: str) -> Dict:
        """Get video information using YouTube Data API v3."""
        if not self.api_key:
            raise ValueError("YouTube Data API key is required")
            
        url = f"{self.base_url}/videos"
        params = {
            'part': 'snippet,contentDetails,status',
            'id': video_id,
            'key': self.api_key
        }
        
        response = requests.get(url, params=params)
        response.raise_for_status()
        
        data = response.json()
        if not data.get('items'):
            raise VideoUnavailable(f"Video {video_id} not found")
            
        return data['items'][0]
    
    def get_captions_list(self, video_id: str) -> List[Dict]:
        """Get list of available captions using YouTube Data API v3."""
        if not self.api_key:
            raise ValueError("YouTube Data API key is required")
            
        url = f"{self.base_url}/captions"
        params = {
            'part': 'snippet',
            'videoId': video_id,
            'key': self.api_key
        }
        
        response = requests.get(url, params=params)
        response.raise_for_status()
        
        data = response.json()
        return data.get('items', [])
    
    def check_video_accessibility(self, video_id: str) -> Dict:
        """Check if video is accessible and has captions."""
        try:
            video_info = self.get_video_info(video_id)
            captions = self.get_captions_list(video_id)
            
            return {
                'accessible': True,
                'title': video_info['snippet']['title'],
                'has_captions': len(captions) > 0,
                'caption_languages': [cap['snippet']['language'] for cap in captions],
                'video_status': video_info['status']['privacyStatus']
            }
        except Exception as e:
            return {
                'accessible': False,
                'error': str(e)
            }

def get_transcript_with_api_fallback(video_id: str, api_key: Optional[str] = None) -> str:
    """
    Get YouTube transcript with API fallback strategy.
    
    Strategy:
    1. If API key provided, check video accessibility first
    2. Try transcript API with minimal requests
    3. Provide detailed error information
    """
    
    # Strategy 1: If API key available, check video first
    if api_key:
        try:
            api_helper = YouTubeAPIHelper(api_key)
            video_check = api_helper.check_video_accessibility(video_id)
            
            if not video_check['accessible']:
                raise Exception(f"Video not accessible: {video_check.get('error', 'Unknown error')}")
            
            if not video_check['has_captions']:
                raise TranscriptsDisabled(f"No captions available for video {video_id}")
                
            logger.info(f"Video {video_id} is accessible with captions in: {video_check['caption_languages']}")
            
        except Exception as e:
            logger.warning(f"API check failed: {str(e)}")
    
    # Strategy 2: Try transcript API with single attempt (no retries to avoid bot detection)
    try:
        logger.info(f"Attempting single transcript fetch for video {video_id}")
        
        # Single attempt with no retries
        transcript_chunks = YouTubeTranscriptApi.get_transcript(video_id, ['en', 'en-US', 'en-GB'])
        
        if transcript_chunks:
            transcript = "\n".join([f"{chunk['start']:.2f} - {chunk['text']}" for chunk in transcript_chunks])
            logger.info(f"Successfully extracted transcript, length: {len(transcript)} characters")
            return transcript
        else:
            raise Exception("Empty transcript returned")
            
    except Exception as e:
        error_msg = str(e)
        
        if "no element found" in error_msg.lower():
            raise Exception(
                f"YouTube bot detection active for video {video_id}. "
                "Solutions:\n"
                "1. Wait 15-30 minutes and try again\n"
                "2. Use a different network/IP address\n"
                "3. Set up YouTube Data API v3 key for reliable access\n"
                "4. Try accessing the video manually in browser first"
            )
        elif "TranscriptsDisabled" in error_msg:
            raise TranscriptsDisabled(f"Transcripts are disabled for video {video_id}")
        elif "VideoUnavailable" in error_msg:
            raise VideoUnavailable(f"Video {video_id} is unavailable or private")
        else:
            raise Exception(f"Transcript extraction failed: {error_msg}")

# Configuration function
def configure_youtube_api_key(api_key: str):
    """Configure YouTube Data API v3 key for reliable access."""
    global _youtube_api_key
    _youtube_api_key = api_key
    logger.info("YouTube Data API v3 key configured")

_youtube_api_key = None

def get_youtube_transcript_reliable(video_id: str) -> str:
    """
    Most reliable way to get YouTube transcript.
    Uses API key if configured, otherwise falls back to basic method.
    """
    return get_transcript_with_api_fallback(video_id, _youtube_api_key)
