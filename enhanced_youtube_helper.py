#!/usr/bin/env python3
"""
Enhanced YouTube transcript helper with advanced bot detection avoidance.
"""

import time
import random
import logging
from typing import List, Dict, Optional
from youtube_transcript_api import YouTubeTranscriptApi
from youtube_transcript_api._errors import (
    TranscriptsDisabled, 
    VideoUnavailable, 
    YouTubeRequestFailed,
    IpBlocked,
    VideoUnplayable
)
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

logger = logging.getLogger(__name__)

class EnhancedYouTubeTranscriptHelper:
    """Enhanced helper class for robust YouTube transcript extraction with bot detection avoidance."""
    
    def __init__(self):
        self.last_request_time = 0
        self.min_request_interval = 5.0  # Increased interval
        self.session = self._create_session()
        
        # User agents to rotate through
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/91.0.864.59',
        ]
        self.current_user_agent_index = 0
        
    def _create_session(self):
        """Create a requests session with retry strategy."""
        session = requests.Session()
        
        # Retry strategy
        retry_strategy = Retry(
            total=3,
            backoff_factor=2,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        return session
        
    def _get_next_user_agent(self):
        """Get the next user agent in rotation."""
        user_agent = self.user_agents[self.current_user_agent_index]
        self.current_user_agent_index = (self.current_user_agent_index + 1) % len(self.user_agents)
        return user_agent
        
    def _wait_if_needed(self):
        """Add delay between requests to avoid rate limiting."""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last
            # Add more jitter
            sleep_time += random.uniform(1.0, 3.0)
            logger.info(f"Rate limiting: waiting {sleep_time:.2f} seconds")
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    def get_transcript_with_fallback(self, video_id: str) -> str:
        """
        Get YouTube transcript with multiple fallback strategies.
        
        Args:
            video_id: YouTube video ID
            
        Returns:
            Formatted transcript string
            
        Raises:
            Exception: If transcript cannot be retrieved with any method
        """
        
        # Strategy 1: Try with enhanced session and user agent rotation
        try:
            return self._get_transcript_with_session(video_id)
        except Exception as e:
            logger.warning(f"Session-based approach failed: {str(e)}")
        
        # Strategy 2: Try with basic API but longer delays
        try:
            return self._get_transcript_with_delays(video_id)
        except Exception as e:
            logger.warning(f"Delayed approach failed: {str(e)}")
        
        # Strategy 3: Try with cookies (if available)
        try:
            return self._get_transcript_with_cookies(video_id)
        except Exception as e:
            logger.warning(f"Cookie-based approach failed: {str(e)}")
        
        # If all strategies fail, provide helpful error message
        raise Exception(
            f"All transcript extraction strategies failed for video {video_id}. "
            "This is likely due to YouTube bot detection. "
            "Possible solutions:\n"
            "1. Wait 10-15 minutes before trying again\n"
            "2. Use a different network/IP address\n"
            "3. Try accessing the video manually in a browser first\n"
            "4. Check if the video has transcripts enabled"
        )
    
    def _get_transcript_with_session(self, video_id: str) -> str:
        """Try to get transcript using custom session with user agent rotation."""
        logger.info(f"Trying session-based approach for video {video_id}")
        
        # Wait before request
        self._wait_if_needed()
        
        # Rotate user agent
        user_agent = self._get_next_user_agent()
        logger.info(f"Using user agent: {user_agent[:50]}...")
        
        # Create API instance with custom session
        # Note: This is a simplified approach - the actual implementation would need
        # to modify the youtube_transcript_api to accept custom sessions
        
        # For now, fall back to basic approach with delays
        return self._get_transcript_basic(video_id, max_retries=2)
    
    def _get_transcript_with_delays(self, video_id: str) -> str:
        """Try to get transcript with longer delays between requests."""
        logger.info(f"Trying delayed approach for video {video_id}")
        
        # Longer wait
        time.sleep(random.uniform(8, 15))
        
        return self._get_transcript_basic(video_id, max_retries=2)
    
    def _get_transcript_with_cookies(self, video_id: str) -> str:
        """Try to get transcript using cookie-based approach."""
        logger.info(f"Trying cookie-based approach for video {video_id}")
        
        # This would require implementing cookie handling
        # For now, fall back to basic approach
        return self._get_transcript_basic(video_id, max_retries=1)
    
    def _get_transcript_basic(self, video_id: str, max_retries: int = 3) -> str:
        """Basic transcript fetching with retry logic."""
        
        for attempt in range(max_retries):
            try:
                if attempt > 0:
                    # Exponential backoff
                    delay = random.uniform(5, 12) * (2 ** attempt)
                    logger.info(f"Retry attempt {attempt + 1}, waiting {delay:.2f} seconds")
                    time.sleep(delay)
                
                logger.info(f"Attempting basic transcript fetch for video {video_id} (attempt {attempt + 1})")
                
                # Check available transcripts first
                transcript_list = YouTubeTranscriptApi.list_transcripts(video_id)
                available_languages = [t.language_code for t in transcript_list]
                logger.info(f"Available languages: {available_languages}")
                
                if not available_languages:
                    raise TranscriptsDisabled(f"No transcripts available for video {video_id}")
                
                # Try language combinations with delays between each
                language_preferences = [
                    ['en'],
                    ['en-US'],
                    ['en-GB'],
                    available_languages[:1] if available_languages else [],
                ]
                
                for i, languages in enumerate(language_preferences):
                    if not languages:
                        continue
                        
                    try:
                        if i > 0:  # Add delay between language attempts
                            time.sleep(random.uniform(2, 4))
                        
                        logger.info(f"Trying languages: {languages}")
                        transcript_chunks = YouTubeTranscriptApi.get_transcript(video_id, languages)
                        
                        if transcript_chunks:
                            logger.info(f"Success with languages {languages}")
                            return self._format_transcript(transcript_chunks)
                            
                    except Exception as lang_error:
                        error_msg = str(lang_error)
                        if "no element found" in error_msg.lower():
                            logger.warning(f"Bot detection with languages {languages}")
                            break  # Don't try other languages if bot detected
                        else:
                            logger.warning(f"Failed with languages {languages}: {error_msg}")
                            continue
                
                # If we get here, all language attempts failed
                raise Exception(f"All language attempts failed for video {video_id}")
                
            except (TranscriptsDisabled, VideoUnavailable, VideoUnplayable) as e:
                # These errors won't be fixed by retrying
                raise e
                
            except Exception as e:
                error_msg = str(e)
                logger.warning(f"Attempt {attempt + 1} failed: {error_msg}")
                
                if "no element found" in error_msg.lower():
                    if attempt < max_retries - 1:
                        continue  # Retry with longer delay
                    else:
                        raise Exception(f"Bot detection persists after {max_retries} attempts")
                
                if attempt == max_retries - 1:
                    raise e
        
        raise Exception(f"Failed after {max_retries} attempts")
    
    def _format_transcript(self, transcript_chunks: List[Dict]) -> str:
        """Format transcript chunks into a readable string."""
        if not transcript_chunks:
            return "No transcript content available."
        
        formatted_lines = []
        for chunk in transcript_chunks:
            start_time = chunk.get('start', 0)
            text = chunk.get('text', '').strip()
            if text:
                formatted_lines.append(f"{start_time:.2f} - {text}")
        
        return "\n".join(formatted_lines)

# Global instance
enhanced_helper = EnhancedYouTubeTranscriptHelper()

def get_youtube_transcript_enhanced(video_id: str) -> str:
    """
    Enhanced function to get formatted YouTube transcript with fallback strategies.
    
    Args:
        video_id: YouTube video ID
        
    Returns:
        Formatted transcript string
        
    Raises:
        Exception: If transcript cannot be retrieved with any strategy
    """
    try:
        return enhanced_helper.get_transcript_with_fallback(video_id)
    except Exception as e:
        logger.error(f"All strategies failed for video {video_id}: {str(e)}")
        raise e
