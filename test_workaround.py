#!/usr/bin/env python3
"""
Test the YouTube workaround solution.
"""

import logging
from youtube_workaround import get_youtube_transcript_safe, get_session_status, reset_session

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_workaround():
    """Test the YouTube workaround approach."""
    
    print("=== YouTube Workaround Test ===")
    print("This approach:")
    print("- Limits requests to avoid bot detection")
    print("- Adds delays between requests")
    print("- Uses minimal API calls")
    print("- Provides clear error guidance")
    
    # Check session status
    status = get_session_status()
    print(f"\nSession Status:")
    print(f"- Requests remaining: {status['requests_remaining']}")
    print(f"- Can make request: {status['can_make_request']}")
    
    # Test with a simple video
    video_id = "dQw4w9WgXcQ"  # Rick Roll
    
    print(f"\nTesting with video: {video_id}")
    print("Note: This will wait 10+ seconds to avoid bot detection...")
    
    try:
        transcript = get_youtube_transcript_safe(video_id)
        
        if transcript and len(transcript) > 50:
            print("✅ SUCCESS: Transcript extracted!")
            print(f"   Length: {len(transcript)} characters")
            
            # Show sample
            lines = transcript.split('\n')[:3]
            print("   Sample:")
            for line in lines:
                print(f"     {line}")
            
            # Show updated status
            status = get_session_status()
            print(f"\nUpdated session status:")
            print(f"- Requests remaining: {status['requests_remaining']}")
            
            return True
        else:
            print("❌ Empty transcript returned")
            return False
            
    except Exception as e:
        print(f"❌ FAILED: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_workaround()
    
    if success:
        print("\n🎉 Workaround is working!")
        print("\nFor your application:")
        print("1. YouTube videos will now be processed safely")
        print("2. Maximum 3 videos per session to avoid bot detection")
        print("3. 10+ second delays between requests")
        print("4. Clear error messages when limits are reached")
        
        print("\nTips:")
        print("- Process YouTube videos sparingly (1-2 per hour)")
        print("- Restart application if you hit the session limit")
        print("- Wait 30+ minutes if you get bot detection errors")
    else:
        print("\n⚠️  Issue detected:")
        print("1. Your IP may still be temporarily blocked")
        print("2. Wait 30-60 minutes before trying again")
        print("3. Try from a different network if possible")
        print("4. The video may not have English transcripts")
