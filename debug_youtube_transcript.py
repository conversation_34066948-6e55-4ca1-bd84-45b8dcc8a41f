#!/usr/bin/env python3
"""
Debug script to test YouTube transcript extraction and identify issues.
"""

import sys
import logging
from youtube_transcript_api import YouTubeTranscriptApi
from youtube_transcript_api._errors import (
    TranscriptsDisabled, 
    VideoUnavailable, 
    YouTubeRequestFailed,
    IpBlocked,
    VideoUnplayable
)
import re
from typing import Optional

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def extract_video_id(yt_link: str) -> Optional[str]:
    """Extract video ID from YouTube URL."""
    patterns = [
        r"^https?://(?:www\.)?youtube\.com/watch\?v=([\w-]+)",
        r"^https?://(?:www\.)?youtube\.com/embed/([\w-]+)",
        r"^https?://youtu\.be/([\w-]+)",  # youtu.be does not use www
    ]
    for pattern in patterns:
        match = re.search(pattern, yt_link)
        if match:
            return match.group(1)
    return None

def debug_youtube_transcript(video_url: str):
    """Debug YouTube transcript extraction for a given video URL."""
    print(f"\n=== Debugging YouTube Transcript for: {video_url} ===")
    
    # Extract video ID
    video_id = extract_video_id(video_url)
    if not video_id:
        print(f"❌ ERROR: Could not extract video ID from URL: {video_url}")
        return
    
    print(f"✅ Video ID extracted: {video_id}")
    
    try:
        # Step 1: Try to list available transcripts
        print("\n--- Step 1: Listing available transcripts ---")
        transcript_list = YouTubeTranscriptApi.list_transcripts(video_id)
        
        available_transcripts = []
        for transcript in transcript_list:
            available_transcripts.append({
                'language': transcript.language,
                'language_code': transcript.language_code,
                'is_generated': transcript.is_generated,
                'is_translatable': transcript.is_translatable
            })
        
        print(f"✅ Available transcripts: {len(available_transcripts)}")
        for t in available_transcripts:
            print(f"   - {t['language_code']} ({t['language']}) - Generated: {t['is_generated']}")
        
        if not available_transcripts:
            print("❌ No transcripts available for this video")
            return
            
        # Step 2: Try to fetch transcript with different language preferences
        print("\n--- Step 2: Fetching transcript ---")
        
        # Try different language combinations
        language_preferences = [
            ['en'],
            ['en-US'],
            ['en-GB'], 
            ['en', 'en-US', 'en-GB'],
            [t['language_code'] for t in available_transcripts[:3]]  # Try first 3 available
        ]
        
        transcript_chunks = None
        used_languages = None
        
        for languages in language_preferences:
            try:
                print(f"   Trying languages: {languages}")
                transcript_chunks = YouTubeTranscriptApi.get_transcript(video_id, languages)
                used_languages = languages
                print(f"   ✅ Success with languages: {languages}")
                break
            except Exception as e:
                print(f"   ❌ Failed with languages {languages}: {str(e)}")
                continue
        
        if not transcript_chunks:
            print("❌ Could not fetch transcript with any language preference")
            return
            
        # Step 3: Analyze the transcript
        print(f"\n--- Step 3: Transcript Analysis ---")
        print(f"✅ Transcript fetched successfully using languages: {used_languages}")
        print(f"   Number of chunks: {len(transcript_chunks)}")
        
        if transcript_chunks:
            first_chunk = transcript_chunks[0]
            last_chunk = transcript_chunks[-1]
            
            print(f"   First chunk: {first_chunk}")
            print(f"   Last chunk: {last_chunk}")
            
            total_duration = last_chunk.get('start', 0) + last_chunk.get('duration', 0)
            total_text_length = sum(len(chunk.get('text', '')) for chunk in transcript_chunks)
            
            print(f"   Total duration: {total_duration:.2f} seconds")
            print(f"   Total text length: {total_text_length} characters")
            
            # Sample transcript format
            sample_transcript = "\n".join([
                f"{chunk['start']:.2f} - {chunk['text']}" 
                for chunk in transcript_chunks[:3]
            ])
            print(f"   Sample transcript format:\n{sample_transcript}")
            
        print("\n✅ SUCCESS: Transcript extraction completed successfully!")
        
    except TranscriptsDisabled as e:
        print(f"❌ ERROR: Transcripts are disabled for this video: {str(e)}")
    except VideoUnavailable as e:
        print(f"❌ ERROR: Video is unavailable: {str(e)}")
    except IpBlocked as e:
        print(f"❌ ERROR: IP blocked by YouTube (bot detection): {str(e)}")
        print("   This might be due to:")
        print("   - Too many requests from your IP")
        print("   - YouTube detecting automated access")
        print("   - Need to use proxies or wait before retrying")
    except VideoUnplayable as e:
        print(f"❌ ERROR: Video is unplayable: {str(e)}")
    except YouTubeRequestFailed as e:
        print(f"❌ ERROR: YouTube request failed: {str(e)}")
        print("   This might be due to:")
        print("   - Network connectivity issues")
        print("   - YouTube API changes")
        print("   - Rate limiting")
    except Exception as e:
        print(f"❌ UNEXPECTED ERROR: {type(e).__name__}: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # Test with a few different video URLs
    test_urls = [
        # Add your problematic URL here
        "https://www.youtube.com/watch?v=dQw4w9WgXcQ",  # Rick Roll (should have transcripts)
        "https://www.youtube.com/watch?v=jNQXAC9IVRw",  # Me at the zoo (first YouTube video)
    ]
    
    if len(sys.argv) > 1:
        # Use URL from command line argument
        test_urls = [sys.argv[1]]
    
    for url in test_urls:
        debug_youtube_transcript(url)
        print("\n" + "="*80 + "\n")
