#!/usr/bin/env python3
"""
Test script to verify the YouTube transcript fix.
"""

import sys
import logging
from youtube_transcript_helper import get_youtube_transcript
import re

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def extract_video_id(yt_link: str):
    """Extract video ID from YouTube URL."""
    patterns = [
        r"^https?://(?:www\.)?youtube\.com/watch\?v=([\w-]+)",
        r"^https?://(?:www\.)?youtube\.com/embed/([\w-]+)",
        r"^https?://youtu\.be/([\w-]+)",
    ]
    for pattern in patterns:
        match = re.search(pattern, yt_link)
        if match:
            return match.group(1)
    return None

def test_youtube_transcript(video_url: str):
    """Test YouTube transcript extraction with the new helper."""
    print(f"\n=== Testing YouTube Transcript Fix for: {video_url} ===")
    
    # Extract video ID
    video_id = extract_video_id(video_url)
    if not video_id:
        print(f"❌ ERROR: Could not extract video ID from URL: {video_url}")
        return False
    
    print(f"✅ Video ID extracted: {video_id}")
    
    try:
        # Test the helper function
        print("\n--- Testing transcript extraction ---")
        transcript = get_youtube_transcript(video_id)
        
        if not transcript or transcript.strip() == "No transcript content available.":
            print("❌ No transcript content returned")
            return False
        
        print(f"✅ Transcript extracted successfully!")
        print(f"   Length: {len(transcript)} characters")
        
        # Show first few lines
        lines = transcript.split('\n')
        print(f"   Number of lines: {len(lines)}")
        print("   First few lines:")
        for i, line in enumerate(lines[:5]):
            print(f"     {i+1}: {line}")
        
        if len(lines) > 5:
            print("     ...")
            print(f"     {len(lines)}: {lines[-1]}")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {str(e)}")
        return False

if __name__ == "__main__":
    # Test URLs
    test_urls = [
        "https://www.youtube.com/watch?v=dQw4w9WgXcQ",  # Rick Roll
        "https://www.youtube.com/watch?v=jNQXAC9IVRw",  # Me at the zoo
    ]
    
    if len(sys.argv) > 1:
        # Use URL from command line
        test_urls = [sys.argv[1]]
    
    success_count = 0
    total_count = len(test_urls)
    
    for url in test_urls:
        if test_youtube_transcript(url):
            success_count += 1
        print("\n" + "="*80)
    
    print(f"\n=== SUMMARY ===")
    print(f"Successful: {success_count}/{total_count}")
    
    if success_count == total_count:
        print("✅ All tests passed! The YouTube transcript fix is working.")
    else:
        print("❌ Some tests failed. Check the error messages above.")
