#!/usr/bin/env python3
"""
YouTube Data API v3 implementation for reliable transcript access.
This uses the official YouTube Data API v3 to avoid bot detection entirely.
"""

import logging
import time
import os
import requests
from typing import Optional, Dict, List
from youtube_transcript_api import YouTubeTranscriptApi
from youtube_transcript_api._errors import TranscriptsDisabled, VideoUnavailable

logger = logging.getLogger(__name__)

class YouTubeAPIV3:
    """YouTube Data API v3 implementation for reliable access."""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://www.googleapis.com/youtube/v3"
        self.session = requests.Session()
        
    def get_video_info(self, video_id: str) -> Dict:
        """Get video information using YouTube Data API v3."""
        url = f"{self.base_url}/videos"
        params = {
            'part': 'snippet,contentDetails,status',
            'id': video_id,
            'key': self.api_key
        }
        
        try:
            response = self.session.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            if not data.get('items'):
                raise VideoUnavailable(f"Video {video_id} not found or is private")
                
            return data['items'][0]
        except requests.exceptions.RequestException as e:
            if "403" in str(e):
                raise Exception(f"YouTube Data API v3 access denied. Please check:\n1. API key is valid\n2. YouTube Data API v3 is enabled\n3. API key has proper permissions")
            elif "404" in str(e):
                raise VideoUnavailable(f"Video {video_id} not found")
            else:
                raise Exception(f"YouTube API request failed: {str(e)}")
    
    def get_captions_list(self, video_id: str) -> List[Dict]:
        """Get list of available captions using YouTube Data API v3."""
        url = f"{self.base_url}/captions"
        params = {
            'part': 'snippet',
            'videoId': video_id,
            'key': self.api_key
        }
        
        try:
            response = self.session.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            return data.get('items', [])
        except requests.exceptions.RequestException as e:
            logger.warning(f"Could not get captions list: {str(e)}")
            return []
    
    def check_video_accessibility(self, video_id: str) -> Dict:
        """Check if video is accessible and has captions."""
        try:
            video_info = self.get_video_info(video_id)
            captions = self.get_captions_list(video_id)
            
            return {
                'accessible': True,
                'title': video_info['snippet']['title'],
                'has_captions': len(captions) > 0,
                'caption_languages': [cap['snippet']['language'] for cap in captions],
                'video_status': video_info['status']['privacyStatus'],
                'duration': video_info['contentDetails']['duration']
            }
        except Exception as e:
            return {
                'accessible': False,
                'error': str(e)
            }

class YouTubeTranscriptManager:
    """Manager class that combines API v3 with transcript extraction."""
    
    def __init__(self, api_key: Optional[str] = None):
        self.api_key = api_key
        self.youtube_api = YouTubeAPIV3(api_key) if api_key else None
        self.request_count = 0
        self.max_requests_per_hour = 10  # Conservative limit
        
    def get_transcript_with_api(self, video_id: str) -> str:
        """Get transcript using YouTube Data API v3 + transcript API."""
        
        if not self.api_key:
            raise Exception("YouTube Data API v3 key required for reliable access")
        
        try:
            # Step 1: Check video accessibility using API v3
            logger.info(f"Checking video accessibility for {video_id}")
            video_check = self.youtube_api.check_video_accessibility(video_id)
            
            if not video_check['accessible']:
                raise Exception(f"Video not accessible: {video_check.get('error', 'Unknown error')}")
            
            logger.info(f"Video '{video_check['title']}' is accessible")
            logger.info(f"Privacy status: {video_check['video_status']}")
            logger.info(f"Has captions: {video_check['has_captions']}")
            
            if video_check['has_captions']:
                logger.info(f"Available caption languages: {video_check['caption_languages']}")
            
            # Step 2: Try to get transcript using transcript API
            logger.info(f"Attempting transcript extraction for video {video_id}")
            
            # Try different language preferences
            language_preferences = ['en', 'en-US', 'en-GB']
            if video_check['caption_languages']:
                # Add available languages to preferences
                for lang in video_check['caption_languages']:
                    if lang not in language_preferences:
                        language_preferences.append(lang)
            
            for languages in [language_preferences[:1], language_preferences[:3]]:
                try:
                    logger.info(f"Trying languages: {languages}")
                    transcript_chunks = YouTubeTranscriptApi.get_transcript(video_id, languages)
                    
                    if transcript_chunks:
                        transcript = "\n".join([f"{chunk['start']:.2f} - {chunk['text']}" for chunk in transcript_chunks])
                        logger.info(f"Successfully extracted transcript, length: {len(transcript)} characters")
                        self.request_count += 1
                        return transcript
                        
                except Exception as e:
                    logger.warning(f"Failed with languages {languages}: {str(e)}")
                    continue
            
            # If we get here, transcript extraction failed
            if video_check['has_captions']:
                raise Exception(f"Video has captions but transcript extraction failed. Available languages: {video_check['caption_languages']}")
            else:
                raise TranscriptsDisabled(f"No captions available for video {video_id}")
                
        except Exception as e:
            error_msg = str(e)
            
            if "API key" in error_msg or "403" in error_msg:
                raise Exception(
                    f"YouTube Data API v3 error: {error_msg}\n"
                    "Please check:\n"
                    "1. API key is valid\n"
                    "2. YouTube Data API v3 is enabled in Google Cloud Console\n"
                    "3. API key has proper permissions"
                )
            elif "TranscriptsDisabled" in error_msg:
                raise TranscriptsDisabled(error_msg)
            elif "VideoUnavailable" in error_msg:
                raise VideoUnavailable(error_msg)
            else:
                raise Exception(f"Transcript extraction failed: {error_msg}")

# Global instance - will be configured with API key
youtube_manager = None

def configure_youtube_api(api_key: str):
    """Configure YouTube Data API v3 key."""
    global youtube_manager
    youtube_manager = YouTubeTranscriptManager(api_key)
    logger.info("YouTube Data API v3 configured successfully")

def get_youtube_transcript_safe(video_id: str) -> str:
    """
    Safe YouTube transcript extraction using YouTube Data API v3.
    
    This function:
    - Uses official YouTube Data API v3 to avoid bot detection
    - Checks video accessibility before attempting transcript extraction
    - Provides detailed error messages
    - Handles various error conditions gracefully
    
    Args:
        video_id: YouTube video ID
        
    Returns:
        Formatted transcript string
        
    Raises:
        Exception: With specific guidance on how to resolve issues
    """
    global youtube_manager
    
    if not youtube_manager:
        # Try to get API key from environment
        api_key = os.getenv("YOUTUBE_API_KEY")
        if api_key:
            configure_youtube_api(api_key)
        else:
            raise Exception(
                "YouTube Data API v3 key not configured.\n"
                "Please:\n"
                "1. Set YOUTUBE_API_KEY environment variable\n"
                "2. Or call configure_youtube_api(api_key) first\n"
                "3. Get API key from Google Cloud Console with YouTube Data API v3 enabled"
            )
    
    return youtube_manager.get_transcript_with_api(video_id)

def get_session_status() -> dict:
    """Get current session status."""
    global youtube_manager
    
    if not youtube_manager:
        return {
            'configured': False,
            'requests_made': 0,
            'max_requests': 0,
            'api_available': False
        }
    
    return {
        'configured': True,
        'requests_made': youtube_manager.request_count,
        'max_requests': youtube_manager.max_requests_per_hour,
        'requests_remaining': youtube_manager.max_requests_per_hour - youtube_manager.request_count,
        'api_available': youtube_manager.api_key is not None
    }

def reset_session():
    """Reset the session to allow more requests."""
    global youtube_manager
    if youtube_manager:
        youtube_manager.request_count = 0
        logger.info("YouTube session reset - request count cleared")
