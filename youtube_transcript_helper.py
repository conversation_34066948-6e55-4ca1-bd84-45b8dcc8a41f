#!/usr/bin/env python3
"""
Enhanced YouTube transcript helper with bot detection avoidance.
"""

import time
import random
import logging
from typing import List, Dict, Optional
from youtube_transcript_api import YouTubeTranscriptApi
from youtube_transcript_api._errors import (
    TranscriptsDisabled, 
    VideoUnavailable, 
    YouTubeRequestFailed,
    IpBlocked,
    VideoUnplayable
)

logger = logging.getLogger(__name__)

class YouTubeTranscriptHelper:
    """Helper class for robust YouTube transcript extraction."""
    
    def __init__(self):
        self.last_request_time = 0
        self.min_request_interval = 2.0  # Minimum seconds between requests
        
    def _wait_if_needed(self):
        """Add delay between requests to avoid rate limiting."""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last
            # Add some jitter
            sleep_time += random.uniform(0.5, 1.5)
            logger.info(f"Rate limiting: waiting {sleep_time:.2f} seconds")
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    def get_transcript_with_retry(self, video_id: str, max_retries: int = 3) -> List[Dict]:
        """
        Get YouTube transcript with retry logic and bot detection handling.
        
        Args:
            video_id: YouTube video ID
            max_retries: Maximum number of retry attempts
            
        Returns:
            List of transcript chunks
            
        Raises:
            Exception: If transcript cannot be retrieved after all retries
        """
        
        for attempt in range(max_retries):
            try:
                # Add delay between requests
                self._wait_if_needed()
                
                if attempt > 0:
                    # Exponential backoff with jitter for retries
                    delay = random.uniform(3, 8) * (2 ** attempt)
                    logger.info(f"Retry attempt {attempt + 1}, waiting {delay:.2f} seconds")
                    time.sleep(delay)
                
                logger.info(f"Attempting to fetch transcript for video {video_id} (attempt {attempt + 1})")
                
                # First, check available transcripts
                try:
                    transcript_list = YouTubeTranscriptApi.list_transcripts(video_id)
                    available_languages = [t.language_code for t in transcript_list]
                    logger.info(f"Available languages: {available_languages}")
                    
                    if not available_languages:
                        raise TranscriptsDisabled(f"No transcripts available for video {video_id}")
                        
                except Exception as list_error:
                    if "no element found" in str(list_error).lower():
                        if attempt < max_retries - 1:
                            logger.warning(f"Bot detection when listing transcripts, retrying...")
                            continue
                        else:
                            raise Exception(f"Bot detection: Cannot access video {video_id} after {max_retries} attempts")
                    raise list_error
                
                # Try different language combinations
                language_preferences = [
                    ['en'],  # English first
                    ['en-US'],  # US English
                    ['en-GB'],  # UK English  
                    available_languages[:1] if available_languages else [],  # First available
                    available_languages[:2] if len(available_languages) > 1 else [],  # First 2 available
                ]
                
                # Remove empty language lists
                language_preferences = [langs for langs in language_preferences if langs]
                
                for languages in language_preferences:
                    try:
                        logger.info(f"Trying languages: {languages}")
                        
                        # Add small delay between language attempts
                        time.sleep(random.uniform(0.5, 1.0))
                        
                        transcript_chunks = YouTubeTranscriptApi.get_transcript(video_id, languages)
                        
                        if transcript_chunks:
                            logger.info(f"Successfully fetched {len(transcript_chunks)} transcript chunks with languages {languages}")
                            return transcript_chunks
                            
                    except Exception as lang_error:
                        error_msg = str(lang_error)
                        
                        if "no element found" in error_msg.lower():
                            logger.warning(f"Bot detection with languages {languages}")
                            # Don't try other languages if we hit bot detection
                            break
                        else:
                            logger.warning(f"Failed with languages {languages}: {error_msg}")
                            continue
                
                # If we get here, all language attempts failed for this retry
                if attempt < max_retries - 1:
                    logger.warning(f"All language attempts failed for attempt {attempt + 1}, retrying...")
                    continue
                else:
                    raise Exception(f"Failed to fetch transcript with any available language")
                
            except (TranscriptsDisabled, VideoUnavailable, VideoUnplayable) as e:
                # These errors won't be fixed by retrying
                logger.error(f"Permanent error for video {video_id}: {str(e)}")
                raise e
                
            except Exception as e:
                error_msg = str(e)
                logger.warning(f"Attempt {attempt + 1} failed: {error_msg}")
                
                # Check if this is a bot detection issue
                if "no element found" in error_msg.lower() or "bot detection" in error_msg.lower():
                    if attempt < max_retries - 1:
                        logger.info(f"Bot detection detected, will retry with longer delay...")
                        continue
                    else:
                        raise Exception(f"YouTube bot detection: Cannot access video {video_id} after {max_retries} attempts. Try again later.")
                
                # For other errors, don't retry unless it's the last attempt
                if attempt == max_retries - 1:
                    raise e
        
        raise Exception(f"Failed to fetch transcript for video {video_id} after {max_retries} attempts")

    def format_transcript(self, transcript_chunks: List[Dict]) -> str:
        """Format transcript chunks into a readable string."""
        if not transcript_chunks:
            return "No transcript content available."
        
        formatted_lines = []
        for chunk in transcript_chunks:
            start_time = chunk.get('start', 0)
            text = chunk.get('text', '').strip()
            if text:
                formatted_lines.append(f"{start_time:.2f} - {text}")
        
        return "\n".join(formatted_lines)

# Global instance for reuse
youtube_helper = YouTubeTranscriptHelper()

def get_youtube_transcript(video_id: str) -> str:
    """
    Convenience function to get formatted YouTube transcript.
    
    Args:
        video_id: YouTube video ID
        
    Returns:
        Formatted transcript string
        
    Raises:
        Exception: If transcript cannot be retrieved
    """
    try:
        transcript_chunks = youtube_helper.get_transcript_with_retry(video_id)
        return youtube_helper.format_transcript(transcript_chunks)
    except Exception as e:
        logger.error(f"Failed to get transcript for video {video_id}: {str(e)}")
        raise e
