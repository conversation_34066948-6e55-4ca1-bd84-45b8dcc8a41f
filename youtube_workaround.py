#!/usr/bin/env python3
"""
YouTube transcript workaround that avoids bot detection.
This uses a different approach to minimize requests and avoid triggering YouTube's bot detection.
"""

import logging
import time
import random
from typing import Optional
from youtube_transcript_api import YouTubeTranscript<PERSON>pi
from youtube_transcript_api._errors import TranscriptsDisabled, VideoUnavailable

logger = logging.getLogger(__name__)

class YouTubeWorkaround:
    """Workaround class that minimizes requests to avoid bot detection."""
    
    def __init__(self):
        self.last_request_time = 0
        self.min_interval = 10.0  # Minimum 10 seconds between requests
        self.request_count = 0
        self.max_requests_per_session = 3  # Limit requests per session
    
    def _should_wait(self) -> bool:
        """Check if we should wait before making a request."""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        # Always wait minimum interval
        if time_since_last < self.min_interval:
            return True
            
        # Limit requests per session
        if self.request_count >= self.max_requests_per_session:
            logger.warning("Request limit reached for this session. Please restart application or wait.")
            return True
            
        return False
    
    def _wait_before_request(self):
        """Wait appropriate time before making request."""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.min_interval:
            wait_time = self.min_interval - time_since_last
            # Add jitter
            wait_time += random.uniform(2, 5)
            logger.info(f"Waiting {wait_time:.1f} seconds to avoid rate limiting...")
            time.sleep(wait_time)
        
        self.last_request_time = time.time()
        self.request_count += 1
    
    def get_transcript_minimal(self, video_id: str) -> str:
        """Get transcript with minimal requests to avoid bot detection."""
        
        if self._should_wait():
            if self.request_count >= self.max_requests_per_session:
                raise Exception(
                    f"Request limit reached ({self.max_requests_per_session} requests per session). "
                    "To avoid bot detection, please:\n"
                    "1. Restart your application\n"
                    "2. Wait 30 minutes before trying again\n"
                    "3. Try from a different network if possible"
                )
        
        self._wait_before_request()
        
        try:
            logger.info(f"Making minimal transcript request for video {video_id} (request {self.request_count})")
            
            # Single attempt with most common language
            transcript_chunks = YouTubeTranscriptApi.get_transcript(video_id, ['en'])
            
            if transcript_chunks:
                transcript = "\n".join([f"{chunk['start']:.2f} - {chunk['text']}" for chunk in transcript_chunks])
                logger.info(f"Success! Transcript length: {len(transcript)} characters")
                return transcript
            else:
                raise Exception("Empty transcript returned")
                
        except Exception as e:
            error_msg = str(e)
            
            if "no element found" in error_msg.lower():
                raise Exception(
                    f"YouTube bot detection active. Your IP is temporarily blocked.\n"
                    "Solutions:\n"
                    "1. Wait 30-60 minutes before trying again\n"
                    "2. Use a different network/IP address\n"
                    "3. Try accessing the video manually in browser first\n"
                    "4. Restart your router to get a new IP (if using dynamic IP)"
                )
            elif "429" in error_msg or "Too Many Requests" in error_msg:
                raise Exception(
                    f"Rate limit exceeded. YouTube is blocking too many requests.\n"
                    "Solutions:\n"
                    "1. Wait 30-60 minutes\n"
                    "2. Use a different network\n"
                    "3. Limit YouTube video processing to 1-2 per hour"
                )
            elif "Could not retrieve a transcript" in error_msg:
                if "en" in error_msg:
                    # Try with auto-generated
                    try:
                        logger.info("Trying auto-generated transcript...")
                        time.sleep(2)  # Small delay
                        transcript_chunks = YouTubeTranscriptApi.get_transcript(video_id, ['en-US'])
                        if transcript_chunks:
                            transcript = "\n".join([f"{chunk['start']:.2f} - {chunk['text']}" for chunk in transcript_chunks])
                            return transcript
                    except:
                        pass
                
                raise TranscriptsDisabled(f"No English transcripts available for video {video_id}")
            elif "VideoUnavailable" in error_msg:
                raise VideoUnavailable(f"Video {video_id} is unavailable or private")
            else:
                raise Exception(f"Transcript extraction failed: {error_msg}")

# Global instance
youtube_workaround = YouTubeWorkaround()

def get_youtube_transcript_safe(video_id: str) -> str:
    """
    Safe YouTube transcript extraction that minimizes bot detection risk.
    
    This function:
    - Limits requests per session
    - Adds delays between requests
    - Uses minimal API calls
    - Provides clear error messages
    
    Args:
        video_id: YouTube video ID
        
    Returns:
        Formatted transcript string
        
    Raises:
        Exception: With specific guidance on how to resolve issues
    """
    return youtube_workaround.get_transcript_minimal(video_id)

def reset_session():
    """Reset the session to allow more requests."""
    global youtube_workaround
    youtube_workaround.request_count = 0
    youtube_workaround.last_request_time = 0
    logger.info("YouTube session reset - can make new requests")

def get_session_status() -> dict:
    """Get current session status."""
    return {
        'requests_made': youtube_workaround.request_count,
        'max_requests': youtube_workaround.max_requests_per_session,
        'requests_remaining': youtube_workaround.max_requests_per_session - youtube_workaround.request_count,
        'last_request_time': youtube_workaround.last_request_time,
        'can_make_request': not youtube_workaround._should_wait()
    }
