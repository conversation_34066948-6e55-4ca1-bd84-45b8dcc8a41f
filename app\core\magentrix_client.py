import aiohttp
import urllib
from app.api.models.schema import SourceDocument
from typing import List, Any, Optional
from llama_index.core import SimpleDirectoryReader
from langchain_core.documents import Document
import html2text

from llama_index.readers.file import (
    PDFReader,
    PandasExcelReader
)

from docx import Document  as DocxDocument

from app.core.config import app_settings
from youtube_api_helper import get_youtube_transcript_reliable

import requests
import re
import nest_asyncio

from app.utils.util_functions import FileDetector
nest_asyncio.apply()
from langchain_core.runnables import run_in_executor
import logging
import tempfile
import os
import speech_recognition as sr
from pydub import AudioSegment
from urllib.request import Request, urlopen

os.environ["PATH"] += os.pathsep + r"C:\ffmpeg\bin"
logger = logging.getLogger()

class MagentrixClient():
    file_urls = {
            'Document': 'sys/document/getfile',
            'NoteAndAttachment': 'sys/noteandattachment/getfile'
        }
    def __init__(
        self,
        magentrix_token: str,
        url: str
    ) -> None:
        self._magentrix_token = magentrix_token
        self._base_url = url

    def fetchPermission(self, articleIds:list[str] = [], wikiIds:list[str] = [], noteAndAttachmentIds:list[str]=[], documentIds:list[str]=[]) -> Any:
        target_endpoint = f"{self._base_url}/setup/aiassistantsetting/getpermission"
        auth_headers = {'Authorization': self._magentrix_token}
        payload = {'articleIds': articleIds, 'wikiIds': wikiIds, 'noteAndAttachmentIds': noteAndAttachmentIds, 'documentIds': documentIds}
        response = requests.post(target_endpoint, headers=auth_headers, data=payload)
        
        if response.status_code != 200:
            logger.error(f"Permission request error: {response.status_code} {response.content.decode('utf-8')}")
            logger.error(f"Permission request error: {self._magentrix_token} {self._base_url}")
            raise Exception("Permission request error")
            
        logger.info(f"Permission request: Token: {self._magentrix_token} - URL: {self._base_url} - Payload: {payload}")

        return response.json()           

    async def afetchPermission(self, articleIds:list[str] = [], wikiIds:list[str] = [], noteAndAttachmentIds:list[str]=[], documentIds:list[str]=[]) -> Any:
        target_endpoint = f"{self._base_url}/setup/aiassistantsetting/getpermission"
        headers = {'Authorization': self._magentrix_token}
        payload = {'articleIds': articleIds, 'wikiIds': wikiIds, 'noteAndAttachmentIds': noteAndAttachmentIds, 'documentIds': documentIds}
        
        async with aiohttp.ClientSession(headers=headers) as session:
            response = await session.post(target_endpoint, data=payload)
            
            if response.status == 200:
                return await response.json()
            
            raise Exception(response)

    async def aload_file(self, file: SourceDocument, entity: str = "document") -> List[Document]:
        self._update_metadata(file)

        if file.Text:
            return await self._process_text(file)
        
        return await self._process_file(file, entity)
    
    def _update_metadata(self, file: SourceDocument) -> None:
        if file.IndexType and file.Metadata:
            file.Metadata['IndexType'] = file.IndexType

    async def _process_text(self, file: SourceDocument) -> List[Document]:
        if "youtube.com" in file.Text:
            return await self._process_youtube(file)
        
        return [Document(id=file.Id, page_content=file.Text, metadata=file.Metadata)]

    async def _process_youtube(self, file: SourceDocument) -> List[Document]:
        video_id = self._extract_video_id(file.Text)

        if not video_id:
            raise ValueError(f"Supplied URL {file.Text} is not a supported YouTube URL.")

        try:
            logger.info(f"Processing YouTube video: {video_id}")

            # Use the reliable transcript helper (with API fallback)
            transcript = await run_in_executor(None, get_youtube_transcript_reliable, video_id)

            if not transcript or transcript.strip() == "No transcript content available.":
                logger.warning(f"No transcript content returned for video {video_id}")
                return [Document(id=file.Id, page_content="No transcript available for this video.", metadata=file.Metadata)]

            logger.info(f"Successfully extracted transcript for video {video_id}, length: {len(transcript)} characters")
            return [Document(id=file.Id, page_content=transcript, metadata=file.Metadata)]

        except Exception as e:
            logger.error(f"Error processing YouTube video {video_id}: {str(e)}")

            # Provide user-friendly error messages
            error_msg = f"Failed to extract transcript from YouTube video {video_id}: {str(e)}"

            if "TranscriptsDisabled" in str(e) or "No transcripts available" in str(e):
                error_msg = f"Transcripts are not available for video {video_id}"
            elif "VideoUnavailable" in str(e):
                error_msg = f"Video {video_id} is unavailable or private"
            elif "bot detection" in str(e).lower() or "YouTube bot detection" in str(e):
                error_msg = f"YouTube is temporarily blocking requests for video {video_id}. Please try again later."

            return [Document(id=file.Id, page_content=error_msg, metadata=file.Metadata)]

    async def _process_file(self, file: SourceDocument, entity: str) -> List[Document]:
        try:
            url = f"{self._base_url}/{self.file_urls[entity]}?id={file.Id}"
            # result = await run_in_executor(None, urlopen, req)
            # file_name = result.info().get_filename()
            # content_type = result.info().get_content_type()
            # req = Request(url, headers={'Authorization': self._magentrix_token})
            headers = {'Authorization': self._magentrix_token}
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers) as response:
                    content_disposition = response.headers.get('Content-Disposition', '')
                    file_name = file.Id

                    if 'filename=' in content_disposition and 'filename*' not in content_disposition:
                        file_name = content_disposition.split('filename=')[1].strip('"')                  
                    
                    content_type = response.headers.get('content-type', '')     

                    if not file_name:
                        file_name = file.Id

                    file_detector = FileDetector()

                    if file_detector.is_text_file(file_name, content_type):
                        # Try different encodings
                        raw_content = await response.read()
                        text = None
                        encodings = ['utf-8', 'utf-16', 'iso-8859-1', 'cp1252']
                        
                        for encoding in encodings:
                            try:
                                text = raw_content.decode(encoding)
                                break
                            except UnicodeDecodeError:
                                continue
                        
                        if text is None:
                            logger.warning(f"Could not decode file {file.Id} with any known encoding")
                            # Fallback to ignoring problematic characters
                            text = raw_content.decode('utf-8', errors='ignore')
                        
                        return self._extract_text_from_response(text, content_type, file)
                    
                    return await self._process_non_text_file(response, file_name, content_type, file)
        except Exception as e:
            logger.error(f"Error processing file {file.Id} from {entity}: {str(e)}", exc_info=True)
            logger.error(f"URL attempted: {self._base_url}/{self.file_urls[entity]}?id={file.Id}")
            raise Exception(f"Failed to process file: {str(e)}") from e

    def parse_filename(content_disposition: str) -> str | None:
        if not content_disposition:
            return None

        # Try filename* first
        for part in content_disposition.split(";"):
            if part.strip().lower().startswith("filename*="):
                # Extract and decode: filename*=utf-8''the%20file.pdf
                encoding_and_filename = part.split("=", 1)[1].strip()
                try:
                    encoding, _, filename_encoded = encoding_and_filename.split("'", 2)
                    return urllib.parse.unquote(filename_encoded)
                except ValueError:
                    return None

        # Fallback to filename=
        for part in content_disposition.split(";"):
            if part.strip().lower().startswith("filename="):
                filename = part.split("=", 1)[1].strip().strip('"\'')
                return filename

        return None

    def _is_text_file(self, file_name: str, content_type: str) -> bool:
        text_extensions = ["sql", "txt", "cs", "html", "htm"]
        text_content_types = ["text/html", "text/plain", "message/rfc822"]
        
        return (content_type in text_content_types or 
                any(ext in file_name for ext in text_extensions))

    def _extract_text_from_response(self, text, content_type: str, file: SourceDocument) -> List[Document]:       
        if not text:
            logger.warning(f"Empty content received for file ID: {file.Id}")
            return [Document(id=file.Id, page_content="", metadata=file.Metadata)]

        if content_type in ["text/html", "message/rfc822"]:
            text = html2text.html2text(text)
        
        return [Document(id=file.Id, page_content=text, metadata=file.Metadata)]

    async def _process_non_text_file(self, result, file_name: str, content_type: str, file: SourceDocument) -> List[Document]:
        with tempfile.TemporaryDirectory() as temp_dir:
            # Add appropriate extension based on content type if missing
            if '.' not in file_name:
                if "pdf" in content_type:
                    file_name = f"{file_name}.pdf"
                elif "video/mp4" in content_type:
                    file_name = f"{file_name}.mp4"
                elif "audio/mp3" in content_type:
                    file_name = f"{file_name}.mp3"
                elif "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" in content_type:
                    file_name = f"{file_name}.xlsx"
                elif "application/vnd.openxmlformats-officedocument.wordprocessingml.document" in content_type:
                    file_name = f"{file_name}.docx"
            
            filepath = os.path.join(temp_dir, file_name.lower())
            content = await result.read()

            with open(filepath, "wb") as output:
                output.write(content)
            
            import time
            time.sleep(1)
            if "pdf" in content_type:
                return await self._extract_pdf_text(filepath, file)
            elif any(ext in file_name for ext in [".mp4", ".mp3"]):
                temp_filepath = f"{temp_dir}/{file.Id}_{os.getpid()}_converted.wav"
                recognizer = sr.Recognizer()
                sound = AudioSegment.from_file(filepath)  # Replace with your file
                sound = sound.set_channels(1).set_frame_rate(16000)  # Convert to mono, 16 kHz
                sound.export(temp_filepath, format="wav")

                with sr.AudioFile(temp_filepath) as source:
                    audio_data = recognizer.record(source)  # read the entire audio file
                    result = recognizer.recognize_whisper(audio_data, show_dict=True)
                    chunk_text = [f"{self._native_format_time(chunk['start'])} - {chunk['text']}" for chunk in result["segments"]]
                    transcript = "\n".join(chunk_text)

                    return [Document(page_content=transcript, metadata=file.Metadata)]
            elif ".xlsx" in file_name or ".docx" in file_name:
                return await self._extract_office_text(filepath, file_name, file)
            
        logger.error(f"URL: {self._base_url} for File {file.Id} was not supported.")

    async def _extract_pdf_text(self, filepath: str, file: SourceDocument) -> List[Document]:
        loader = SimpleDirectoryReader(input_dir=os.path.dirname(filepath), file_metadata=lambda _: file.Metadata)
        loader.file_extractor = {".pdf": PDFReader()}
        pages = await loader.aload_data()
        doc_text = "\n\n".join([page.get_content() for page in pages])
        
        return [Document(id=file.Id, page_content=doc_text, metadata=file.Metadata)]

    async def _extract_office_text(self, filepath: str, file_name: str, file: SourceDocument) -> List[Document]:
        doc_text = ''

        if ".docx" in file_name:
            wdoc = DocxDocument(filepath)
            doc_text = "\n".join([p.text for p in wdoc.paragraphs])
        elif ".xlsx" in file_name:
            loader = SimpleDirectoryReader(input_dir=os.path.dirname(filepath), file_metadata=lambda _: file.Metadata)
            loader.file_extractor = {".xlsx": PandasExcelReader()}
            pages = await loader.aload_data()
            doc_text = "\n\n".join([page.get_content() for page in pages])
        
        return [Document(id=file.Id, page_content=doc_text, metadata=file.Metadata)]

    def _extract_video_id(self, yt_link) -> Optional[str]:
        patterns = [
            r"^https?://(?:www\.)?youtube\.com/watch\?v=([\w-]+)",
            r"^https?://(?:www\.)?youtube\.com/embed/([\w-]+)",
            r"^https?://youtu\.be/([\w-]+)",  # youtu.be does not use www
        ]
        for pattern in patterns:
            match = re.search(pattern, yt_link)
            if match:
                return match.group(1)

        # return None if no match is found
        return None

    def _native_format_time(self, seconds)-> str:
        # Format seconds into HH:MM:SS format
        hours, remainder = divmod(int(seconds), 3600)
        minutes, seconds = divmod(remainder, 60)
        return f"{hours:02}:{minutes:02}:{seconds:02}"