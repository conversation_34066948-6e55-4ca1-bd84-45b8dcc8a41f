#!/usr/bin/env python3
"""
Test YouTube Data API v3 implementation.
"""

import os
import logging
from youtube_workaround import get_youtube_transcript_safe, get_session_status, configure_youtube_api

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_youtube_api_v3():
    """Test the YouTube Data API v3 approach."""
    
    print("=== YouTube Data API v3 Test ===")
    print("This approach uses the official YouTube Data API v3 to:")
    print("- Check video accessibility before transcript extraction")
    print("- Avoid bot detection entirely")
    print("- Provide detailed error information")
    print("- Handle various video states (private, unavailable, etc.)")
    
    # Configure API key
    api_key = "AIzaSyAurvcLaehP45QQ1Q_q1FtHzLbl_GHxQdE"
    
    print(f"\nConfiguring YouTube Data API v3...")
    try:
        configure_youtube_api(api_key)
        print("✅ API configured successfully")
    except Exception as e:
        print(f"❌ API configuration failed: {str(e)}")
        return False
    
    # Check session status
    status = get_session_status()
    print(f"\nSession Status:")
    print(f"- API configured: {status['configured']}")
    print(f"- API available: {status['api_available']}")
    print(f"- Requests remaining: {status['requests_remaining']}")
    
    # Test videos
    test_videos = [
        {
            'id': 'dQw4w9WgXcQ',
            'name': 'Rick Roll (should have transcripts)',
            'expected': 'success'
        },
        {
            'id': 'jNQXAC9IVRw', 
            'name': 'Me at the zoo (first YouTube video)',
            'expected': 'success'
        }
    ]
    
    success_count = 0
    
    for video in test_videos:
        print(f"\n--- Testing: {video['name']} ---")
        print(f"Video ID: {video['id']}")
        
        try:
            transcript = get_youtube_transcript_safe(video['id'])
            
            if transcript and len(transcript) > 50:
                print("✅ SUCCESS: Transcript extracted!")
                print(f"   Length: {len(transcript)} characters")
                
                # Show sample
                lines = transcript.split('\n')[:2]
                print("   Sample:")
                for line in lines:
                    print(f"     {line}")
                
                success_count += 1
            else:
                print("❌ Empty or very short transcript")
                
        except Exception as e:
            error_msg = str(e)
            print(f"❌ FAILED: {error_msg}")
            
            # Provide specific guidance
            if "API key" in error_msg or "403" in error_msg:
                print("\n💡 API Key Issue:")
                print("1. Verify API key is correct")
                print("2. Enable YouTube Data API v3 in Google Cloud Console")
                print("3. Check API key permissions")
            elif "TranscriptsDisabled" in error_msg:
                print("\n💡 This video doesn't have transcripts/captions")
            elif "VideoUnavailable" in error_msg:
                print("\n💡 Video is private, deleted, or restricted")
    
    # Final status
    status = get_session_status()
    print(f"\nFinal Session Status:")
    print(f"- Requests made: {status['requests_made']}")
    print(f"- Requests remaining: {status['requests_remaining']}")
    
    return success_count > 0

def test_api_key_setup():
    """Test if YouTube Data API v3 is properly set up."""
    
    print("\n=== API Key Setup Test ===")
    
    api_key = "AIzaSyAurvcLaehP45QQ1Q_q1FtHzLbl_GHxQdE"
    
    # Test basic API access
    import requests
    
    print("Testing basic YouTube Data API v3 access...")
    
    # Test with a simple video info request
    url = "https://www.googleapis.com/youtube/v3/videos"
    params = {
        'part': 'snippet',
        'id': 'dQw4w9WgXcQ',  # Rick Roll
        'key': api_key
    }
    
    try:
        response = requests.get(url, params=params, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('items'):
                video_title = data['items'][0]['snippet']['title']
                print(f"✅ API access successful!")
                print(f"   Test video title: {video_title}")
                return True
            else:
                print("❌ API returned empty results")
                return False
        elif response.status_code == 403:
            print("❌ 403 Forbidden - API key issues:")
            print("1. API key may be invalid")
            print("2. YouTube Data API v3 not enabled")
            print("3. API key lacks permissions")
            print(f"   Response: {response.text}")
            return False
        else:
            print(f"❌ API request failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API test failed: {str(e)}")
        return False

if __name__ == "__main__":
    print("YouTube Data API v3 Implementation Test")
    print("=" * 50)
    
    # Test 1: API key setup
    api_setup_ok = test_api_key_setup()
    
    if api_setup_ok:
        print("\n" + "=" * 50)
        # Test 2: Full transcript extraction
        success = test_youtube_api_v3()
        
        if success:
            print("\n🎉 YouTube Data API v3 is working perfectly!")
            print("\nYour application will now:")
            print("✅ Use official YouTube Data API v3")
            print("✅ Avoid bot detection completely")
            print("✅ Check video accessibility before transcript extraction")
            print("✅ Provide detailed error messages")
            print("✅ Handle up to 10 requests per hour safely")
            
            print("\nNext steps:")
            print("1. Try your application with YouTube URLs")
            print("2. Monitor logs for detailed processing information")
            print("3. API v3 quota allows many more requests than scraping")
        else:
            print("\n⚠️  Some issues detected - see error messages above")
    else:
        print("\n❌ API key setup failed - please fix API configuration first")
        print("\nTo fix:")
        print("1. Go to Google Cloud Console")
        print("2. Enable YouTube Data API v3")
        print("3. Create/verify API key")
        print("4. Ensure API key has proper permissions")
