#!/usr/bin/env python3
"""
Quick test to check if YouTube transcript issue is resolved.
"""

import logging
from enhanced_youtube_helper import get_youtube_transcript_enhanced

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def quick_test():
    """Quick test with a known working video."""
    
    # Test with a video that should have transcripts
    video_id = "dQw4w9WgXcQ"  # <PERSON> Roll - known to have transcripts
    
    print(f"Testing YouTube transcript extraction for video: {video_id}")
    print("This may take 30-60 seconds due to bot detection avoidance delays...")
    
    try:
        transcript = get_youtube_transcript_enhanced(video_id)
        
        if transcript and len(transcript) > 50:
            print("✅ SUCCESS: Transcript extracted successfully!")
            print(f"   Length: {len(transcript)} characters")
            print(f"   First 200 characters: {transcript[:200]}...")
            return True
        else:
            print("❌ FAILED: Empty or very short transcript returned")
            return False
            
    except Exception as e:
        print(f"❌ FAILED: {str(e)}")
        return False

if __name__ == "__main__":
    success = quick_test()
    
    if success:
        print("\n🎉 The YouTube transcript fix appears to be working!")
        print("You can now try processing YouTube videos in your application.")
    else:
        print("\n⚠️  The issue persists. Recommendations:")
        print("1. Wait 10-15 minutes and try again")
        print("2. Try from a different network/IP")
        print("3. Consider using alternative transcript sources")
        print("4. Check if the specific video has transcripts enabled")
