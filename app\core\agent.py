from typing import Union
from llama_index.llms.openai import OpenAI
from llama_index.core import Settings
from langchain.output_parsers import PydanticOutputParser
from llama_index.core.tools import FunctionTool
from app.core.magentrix_client import MagentrixClient
from app.core.magentrix_postprocessor import (PermissionPostprocessor, PublishedDocumentPostprocessor)
from llama_index.core import get_response_synthesizer
from llama_index.core.query_engine import RetrieverQueryEngine
from llama_index.core.storage.chat_store import SimpleChatStore
from llama_index.core.memory import ChatSummaryMemoryBuffer
from llama_index.core.postprocessor import LongContextReorder
from app.api.models.schema import SearchResponse, ChatResponse, RetrieveItem, TokenUsageModel, QueryResponse
from app.core.index_service import LangchainIndexService
from app.core.config import app_settings, tokenizer, embedding_tokenizer, langchain_llm, query_llm, langchain_open_llm
from app.core.tools import (CustomRetrieverTool, CustomDetailRetrieverTool)
from llama_index.core.base.llms.types import Message<PERSON>ole
from llama_index.core import PromptTemplate
from langchain_openai import ChatOpenAI
from langchain.retrievers.document_compressors import LLMChainFilter
from langchain_core.runnables import run_in_executor
from app.utils.util_functions import StopwordRemover
from langchain_openai import OpenAI
from langchain.retrievers import ContextualCompressionRetriever
from langchain.retrievers import EnsembleRetriever
from app.core.magentrix_retriever import LangchainToLlammaRetriever
from app.utils.util_functions import MagentrixReActOutputParser, DetailRetrieverInput, ResponseOutput
import nest_asyncio
import logging
from langgraph.prebuilt import create_react_agent

nest_asyncio.apply()
from llama_index.core.callbacks import CallbackManager, TokenCountingHandler
from llama_index.core.utilities.token_counting import TokenCounter
from llama_index.core.base.llms.types import ChatMessage, MessageRole
from llama_index.core.agent import ReActAgent
from langchain_core.prompts import PromptTemplate as LangchainPromptTemplate, ChatPromptTemplate, MessagesPlaceholder
from langchain_core.messages import ToolMessage, AIMessage, HumanMessage, ToolMessage, BaseMessage
from langchain.tools.retriever import create_retriever_tool
from langchain.tools import Tool, StructuredTool

logger = logging.getLogger(__name__)
stopword_remover = StopwordRemover()


class Agent():
    llm = ChatOpenAI(model="gpt-4o-mini", temperature=0)
    def __init__(
        self,
        org_code: str,
        magentrix_client: MagentrixClient | None
    ) -> None:
        self._magentrix_client = magentrix_client
        self._org_code = org_code
        self._langchain_index_service = LangchainIndexService(org_code)
        
        if magentrix_client and magentrix_client._base_url and magentrix_client._magentrix_token:
            self._langchain_index_service.add_magentrix_client(magentrix_client)

    def add_chat_history(self, chat_history):
        self._chat_history = chat_history
    
    def _general_qa(self, question: str) -> str:
        """Use to answer any generic questions by world knowledge if there is no useful tool. Use a detailed plain text question as input to the tool."""

        llm = OpenAI(model="gpt-4")
        response = llm.complete(f"""You are a chatbot designed to answer this question : {question}""")

        return response

    def _no_tool(self) -> str:
        """Use to answer questions if there is no tools to use to generate a response"""

        return "Sorry I can't anwser this question as I don't have enough information"


    def get_tools(self, filters: dict = None, cutoff : float = 0.3):
        try:
            filters = filters or {}
            knowledge_retriever = self._langchain_index_service.get_hybrid_retriever(
                "KnowledgeBase", 
                filters=f"({filters['KnowledgeBase']}) and Status != 'Draft'" if filters.get('KnowledgeBase', None) else "Status != 'Draft'", 
                cutoff=cutoff, 
                weights=[0.8, 0.2]
            )
            attachment_vector_retriever = self._langchain_index_service.get_hybrid_retriever("NoteAndAttachment", filters=filters.get('NoteAndAttachment', None), cutoff=0.5, weights=[0.8, 0.2])
            ensemble_retriever = EnsembleRetriever(
                retrievers=[knowledge_retriever, attachment_vector_retriever], weights=[0.6, 0.4]
            )
            llama_version = LangchainToLlammaRetriever(ensemble_retriever)            
            node_postprocessors = [LongContextReorder()]
            
            if self._magentrix_client._base_url and self._magentrix_client._magentrix_token:
                node_postprocessors.append(PermissionPostprocessor(magentrix_client=self._magentrix_client))
        except ValueError:
            return [FunctionTool.from_defaults(fn=self._no_tool)]
        
        retrieve_tool = CustomRetrieverTool.from_defaults(retriever=llama_version, 
                                                        node_postprocessors=node_postprocessors,
                                                        name="knowledge_base_tool", 
                                                        description="useful for when you want to answer query about knowledge base")
        
        detail_retrieve_tool = CustomDetailRetrieverTool.from_defaults(retriever=LangchainToLlammaRetriever(knowledge_retriever), 
                                                        node_postprocessors=node_postprocessors,
                                                        name="knowledge_base_detail_tool", 
                                                        description="useful for when you want to explore more details inside a Article or Wiki. DO NOT use this tool with the same parameters twice.")

        tools = [retrieve_tool]   

        return tools

    def get_qa_template_str(self) -> str:
        return (
            "As an AI assistant you provide answers based on the most relevant information from given context as knowledge base. The most relevant knowledge base is below.\n"
            "---------------------\n"
            "{context_str}\n"
            "---------------------\n"
            "You always follow these guidelines:\n"
            "- Answer to your best capability and think about synonyms, always refering to source of knowledge base provided.\n"
            "- Do not introduce examples outside of the knowledge base.\n"
            "- If a user asks a question that involves sensitive, illegal, harmful, or unethical topics (e.g., weapon creation, hacking, personal data theft, self-harm, etc.), respond with a polite refusal."
            "- If there are multiple ID groups, please answer for each group individually without mentioning group ID, if possible. \n"
            "- If the question is about coding and relevant examples are found, adapt the example by replacing necessary classes, variables, or functions to match the user's query. Clearly state what has been modified to ensure transparency and accuracy in the response.\n"
            "- If you think the question cannot be fully addressed by the provided context or too board, let them know that more information might be available by refining their question or asking for a deeper exploration. \n"
            "- If you can not answer or no context provided, state that fact and don't mention about given or provided context.\n"
            "Now answer the query: \n Query: {query_str}\n"
            "Answer: "
        )

    async def aquery(self, q: str, filters: dict=None):
        token_counter_query = TokenCountingHandler(
                tokenizer=tokenizer,
                verbose=False
            )
        token_counter = TokenCounter(tokenizer=embedding_tokenizer)
        total_embedding_token = token_counter.get_string_tokens(q)
        callback_manager = CallbackManager([token_counter_query])
        knowledge_retriever = self._langchain_index_service.get_hybrid_retriever("KnowledgeBase", filters=filters.get('KnowledgeBase', None), cutoff=0.2, focus_top=True)
        # attachment_vector_retriever = self._langchain_index_service.get_hybrid_retriever("NoteAndAttachment", filters=None, cutoff=cutoff)
        # ensemble_retriever = EnsembleRetriever(
        #     retrievers=[knowledge_retriever, attachment_vector_retriever], weights=[0.5, 0.5]
        # )
        llama_version = LangchainToLlammaRetriever(knowledge_retriever)
        node_postprocessors = [LongContextReorder(), PublishedDocumentPostprocessor()]
        
        if (self._magentrix_client._base_url and self._magentrix_client._magentrix_token):
            node_postprocessors.append(PermissionPostprocessor(magentrix_client=self._magentrix_client))

        text_qa_template_str = self.get_qa_template_str()
        customize_text_qa_template = PromptTemplate(text_qa_template_str)
        response_synthesizer = get_response_synthesizer(text_qa_template=customize_text_qa_template, callback_manager=callback_manager)
        knowledge_query_engine = RetrieverQueryEngine.from_args(retriever=llama_version,
                                                                llm=query_llm,
                                                                response_synthesizer=response_synthesizer, 
                                                                text_qa_template=customize_text_qa_template, 
                                                                node_postprocessors=node_postprocessors)

        query_response = await knowledge_query_engine.aquery(q)
        response = QueryResponse(query_response.response, TokenUsageModel(token_counter_query.total_llm_token_count, 
                                                                              total_embedding_token,
                                                                              token_counter_query.prompt_llm_token_count, 
                                                                              token_counter_query.completion_llm_token_count,
                                                                              query_llm.model))

        return response 
    
    async def aretrieve(self, query: str, filters: dict={}, cutoff : float=None):
        if not cutoff:
            cutoff = self.calculate_cutoff(query, 0.01)

        items = list()
        knowledge_retriever = self._langchain_index_service.get_hybrid_retriever(name="KnowledgeBase", cutoff=cutoff, filters=filters.get('KnowledgeBase', None))
        attachment_vector_retriever = self._langchain_index_service.get_hybrid_retriever("NoteAndAttachment", filters=filters.get('NoteAndAttachment', None), cutoff=cutoff)
        ensemble_retriever = EnsembleRetriever(
                retrievers=[knowledge_retriever, attachment_vector_retriever], weights=[0.8, 0.2]
            )
        filter = LLMChainFilter.from_llm(langchain_llm)
        compression_retriever = ContextualCompressionRetriever(
            base_compressor=filter, base_retriever=ensemble_retriever
        )
        docs = await compression_retriever.ainvoke(query)
        uquine_node = {}

        for doc in sorted(docs, key=lambda x: x.metadata["score"], reverse=True): 
            if doc.metadata["Id"] not in uquine_node.keys():
                id = doc.metadata["ReferenceId"] if doc.metadata["IndexType"] == "NoteAndAttachment" else doc.metadata["Id"]
                index_type = doc.metadata["IndexType"] 

                if doc.metadata["IndexType"] == "NoteAndAttachment" and "Reference.BlogId" in doc.metadata:
                    index_type = "Article"

                if doc.metadata["IndexType"] == "NoteAndAttachment" and "Reference.WikiId" in doc.metadata:
                    index_type = "Wiki"
                 
                items.append(RetrieveItem(Id=id, IndexType=index_type))
                uquine_node = uquine_node | {f'{doc.metadata["Id"]}':True}
        
        token_counter = TokenCounter(tokenizer=tokenizer)
        total_embedding_token = token_counter.get_string_tokens(query)

        return SearchResponse(items, TokenUsageModel(0, total_embedding_token, 0, 0, langchain_llm.model))
    
    def calculate_cutoff(self, query:str, step:float=0.1) -> float:
        query_without_stopwords = stopword_remover.remove_stopwords(query)
        query_length = len(query_without_stopwords.split())
        base_cutoff = 0.2

        # Dynamic scaling formula (smooth increase based on length)
        cutoff = min(base_cutoff + (query_length * step), 0.7) 

        if query_length == 0:
            cutoff = 0.3

        return cutoff

    async def asuggest_document(self, query: str):
        document_vector_retriever = self._langchain_index_service.get_hybrid_retriever("Document", filters=None, cutoff=0.5, weights=[0.8, 0.2])    
        filter = LLMChainFilter.from_llm(langchain_llm)
        compression_retriever = ContextualCompressionRetriever(
            base_compressor=filter, base_retriever=document_vector_retriever
        )
        docs = await compression_retriever.ainvoke(query)
        uquine_node = {}
        items = list()

        for doc in sorted(docs, key=lambda x: x.metadata["score"], reverse=True): 
            if doc.metadata["Id"] not in uquine_node.keys():
                index_type = doc.metadata["IndexType"]    
                items.append(RetrieveItem(Id=doc.metadata["Id"], IndexType=index_type))
                uquine_node = uquine_node | {f'{doc.metadata["Id"]}':True}
        
        token_counter = TokenCounter(tokenizer=tokenizer)
        total_embedding_token = token_counter.get_string_tokens(query)

        return SearchResponse(items, TokenUsageModel(0, total_embedding_token, 0, 0, langchain_llm.model))

    async def achat(self, q: str, filters: dict = {}):
        # Load chat history
        loaded_chat_store = (
            SimpleChatStore.parse_raw(self._chat_history) if self._chat_history else SimpleChatStore()
        )
        
        # Initialize chat memory
        chat_memory = await run_in_executor(
            None, ChatSummaryMemoryBuffer.from_defaults, None, Settings.llm, loaded_chat_store
        )
        
        # Setup token counting
        token_counter_chat = TokenCountingHandler(tokenizer=tokenizer)
        callback_manager = CallbackManager([token_counter_chat])
        token_counter = TokenCounter(tokenizer=embedding_tokenizer)
        total_embedding_token = token_counter.get_string_tokens(q)
        
        # Retrieve tools
        tools = self.get_tools(filters=filters)
        react_system_header_str = """You are a helpful chatbot designed to answer questions using a knowledge base.
        
        ## **Question GUIDANCE**
        If the question is complex, break it down into smaller, more manageable questions and address each one individually.


        ## Tools
        You have access to a wide variety of tools. You are responsible for using
        the tools in any sequence you deem appropriate to complete the task at hand.
        This may require breaking the task into subtasks and using different tools
        to complete each subtask. 
        - DO NOT use one tool with the same parameters twice
        - Limit 6 tool calls only. Therefore, you should conclude your answer before reaching the limit.

        You have access to the following tools:
        {tool_desc}

        ## **Guard GUIDANCE**
        - If the question is about chatbot capabilities, what chatbot can answer, what they can ask or similiar questions, please give Answer that you can help answer your questions using information from the knowledge base!
        - If a user asks a question that involves sensitive, illegal, harmful, or unethical topics (e.g., weapon creation, hacking, personal data theft, self-harm, etc.), respond with a polite refusal.
        - If the user greets (e.g., says "hello", "hi", "hey", "good morning", "good afternoon", etc.), respond with a friendly greeting back."
        - If the user enters a very broad or general query, respond with a polite message asking them to be more specific.
        
        OTHERWISE, you can use the provided tools to answer the question.

        ## **Answer GUIDANCE**
        Never skip the Thought step! Every response must start with reasoning.
        - If the question is about coding and relevant examples are found, adapt the example by replacing necessary classes, variables, or functions to match the user's query. Clearly state what has been modified to ensure transparency and accuracy in the response.
        - If Article/Wiki/NoteAndAttachment items have the same Id, they must come from the same source.

        ## Tool Usage Format
        If you need to use a tool answer the question, please use the following format:    
            Thought: I need to use a tool to help me answer the question.
            Action: tool name (one of {tool_names}) if using a tool.
            Action Input: the input to the tool, in a JSON format representing the kwargs (e.g. {{"input": "hello world", "num_beams": 5}})
            
            - For the Action Input, use valid JSON format. Do NOT do this {{'input': 'hello world', 'num_beams': 5}}. Input for tool should be simple and general. So you can get more information.

        If a tool is used, it will respond tool result in the following format:

        ```
        Observation: tool response
        ```

        ## Reasoning format
        You should keep repeating the above format until you have enough information
        to answer the question without using any more tools. At that point, you MUST respond
        in the one of the following two formats:

        Thought: I can answer without using any more tools.
        Answer: [your answer here]
        OR
        Thought: I cannot answer the question with provided information.
        Answer: [state the fact (DO NOT mention the information about provided tools)]

        ## Current Conversation
        Below is the current conversation consisting of interleaving human and assistant messages. The conversation starts with User's Query:
        """
        react_system_prompt = PromptTemplate(react_system_header_str)
        # Initialize AI agent
        agent = ReActAgent.from_tools(
            tools,
            memory=chat_memory,
            callback_manager=callback_manager,
            system_prompt=(
                "You are a chatbot designed to answer questions about the knowledge base. \n"
                "If the question is about you, respond if possible. \n"
                "Otherwise, ALWAYS use at least one provided tool when answering. \n"
                "Do NOT rely on prior knowledge; only provide factual information from tools. Only connect information if they are the same 'Id' \n"
                "Answer to your best ability, considering synonyms. If unable to answer, state that."
            ),
            verbose=True,
            output_parser=MagentrixReActOutputParser()
        )
        agent.update_prompts({"agent_worker:system_prompt": react_system_prompt})
    
        # Get AI response
        query_response = await agent.achat(q)
        
        # Process response sources
        sources = []
        excluded_keys = {"OrgCode", "pk", "Status", "Tags", "Description", "Summary", "IsTitle", "IsDescription", "Reference.WikiId", "Reference.BlogId"}      
        score_nodes = []

        if query_response.source_nodes:
            score_nodes = [item for item in query_response.source_nodes if item.score and item.score > 0.30]
        if not score_nodes and query_response.sources:
            score_nodes = [item for item in query_response.sources[-1].raw_output if hasattr(item, "score") and item.score and item.score > 0.30]
        
        unique_nodes = set()

        for sn in sorted(score_nodes, key=lambda x: x.score, reverse=True):
            metadata = {k: v for k, v in sn.metadata.items() if k not in excluded_keys}
            if metadata["Id"] not in unique_nodes:
                sources.append(metadata)
                unique_nodes.add(metadata["Id"])
            if len(sources) > 3:
                break
        
        # Attach sources to chat history if relevant
        if sources:
            for h in reversed(loaded_chat_store.store['chat_history']):
                if h.role == MessageRole.ASSISTANT and h.content == query_response.response:
                    h.additional_kwargs = {'sources': sources}
                    break
        
        # Serialize chat history
        chat_store_string = await run_in_executor(None, loaded_chat_store.json)
        
        # Construct response object
        response = ChatResponse(
            chat_store_string,
            TokenUsageModel(
                token_counter_chat.total_llm_token_count,
                total_embedding_token,
                token_counter_chat.prompt_llm_token_count,
                token_counter_chat.completion_llm_token_count,
            ),
        )
        
        return response

    async def achat_native_function_call(self, q: str, filters: dict = {}) -> ChatResponse:
        # Load chat history
        loaded_chat_store = SimpleChatStore.parse_raw(self._chat_history) if self._chat_history else SimpleChatStore()

        # Initialize chat memory
        chat_memory = await run_in_executor(
            None, ChatSummaryMemoryBuffer.from_defaults, None, Settings.llm, loaded_chat_store
        )

        # Prepare knowledge retriever
        knowledge_retriever = self._langchain_index_service.get_hybrid_retriever(
                "KnowledgeBase", 
                filters=f"({filters['KnowledgeBase']}) and Status != 'Draft'" if filters.get('KnowledgeBase') else "Status != 'Draft'", 
                cutoff=0.2, 
                weights=[0.9, 0.1],
                top_k=20,
                search_mode="vector_only"
        )
        attachment_vector_retriever = self._langchain_index_service.get_hybrid_retriever("NoteAndAttachment", filters=filters.get('NoteAndAttachment'), cutoff=0.4, weights=[0.8, 0.2], search_mode="vector_only")
        document_vector_retriever = self._langchain_index_service.get_hybrid_retriever("Document", filters=filters.get('Document'), cutoff=0.4, weights=[0.8, 0.2], search_mode="vector_only")
        ensemble_retriever = EnsembleRetriever(
            retrievers=[knowledge_retriever, attachment_vector_retriever, document_vector_retriever], weights=[0.4, 0.3, 0.3]
        )

        # Create tool with retriever
        retriever_tool = self._create_retriever_tool(ensemble_retriever)

        detail_tool = StructuredTool(
            name="get_detail_by_ids",
            func=knowledge_retriever.get_chunks_by_ids,
            coroutine=knowledge_retriever.aget_chunks_by_ids,
            args_schema=DetailRetrieverInput,
            description=(
                "Retrieve more detail context by ids and query. ONLY use this tool for IndexType is Article or Wiki. "
                "Example input: {'ids': ['doc_id'], 'query': 'search query'}"
            ),
            response_format="content_and_artifact"
        )

        note_attachment_detail_tool = StructuredTool(
            name="get_note_and_attachment_detail_by_ids",
            func=attachment_vector_retriever.get_chunks_by_ids,
            coroutine=attachment_vector_retriever.aget_chunks_by_ids,
            args_schema=DetailRetrieverInput,
            description=(
                "Retrieve more detail context by ids and query. ONLY use this tool for IndexType is NoteAndAttachment."
                "Example input: {'ids': ['doc_id'], 'query': 'search query'}"
            ),
            response_format="content_and_artifact"
        )

        document_detail_tool = StructuredTool(
            name="get_document_detail_by_ids",
            func=document_vector_retriever.get_chunks_by_ids,
            coroutine=document_vector_retriever.aget_chunks_by_ids,
            args_schema=DetailRetrieverInput,
            description=(
                "Retrieve more detail context by ids and query. ONLY use this tool for IndexType is Document."
                "Example input: {'ids': ['doc_id'], 'query': 'search query'}"
            ),
            response_format="content_and_artifact"
        )

        # Prepare chat messages
        histories = await self._convert_llama_chat_store_to_langchain_messages(chat_memory.chat_store) if self._chat_history else []
        
        # Initialize agent
        system_prompt = self._build_system_prompt(histories)
        agent = create_react_agent(
            model=langchain_llm,
            tools=[retriever_tool, detail_tool, note_attachment_detail_tool, document_detail_tool],
            prompt=system_prompt,
            debug=True,
        )

        messages = [HumanMessage(content=q)]

        # Token tracking
        embedding_tokens = TokenCounter(tokenizer=embedding_tokenizer).get_string_tokens(q)
        total_tokens = prompt_tokens = completion_tokens = 0

        # Agent response
        final_agent_state = await agent.ainvoke({"messages": messages})

        # Parse agent response
        total_tokens, prompt_tokens, completion_tokens = self._count_tokens(final_agent_state)

        # Extract metadata
        answer, cited_sources = self._parse_final_response(final_agent_state)
        #structured_response = final_agent_state.get('structured_response', {})
        #answer = structured_response.answer
        #cited_sources = structured_response.cited_ids
        unique_sources = self._extract_sources(final_agent_state, cited_sources)

        # Rebuild chat store
        updated_chat_store = self._update_chat_store(loaded_chat_store, 
                                                    final_agent_state, 
                                                    answer,
                                                    unique_sources)
        chat_store_string = await run_in_executor(None, updated_chat_store.json)

        return ChatResponse(
            chat_store_string,
            TokenUsageModel(total_tokens, embedding_tokens, prompt_tokens, completion_tokens, langchain_llm.model)
        )
    
    def _create_retriever_tool(self, retriever):
        doc_prompt = LangchainPromptTemplate.from_template(
            "<doc>\n{page_content}\n<meta>\nId: {Id}\n IndexType: {IndexType}\n</meta>\n</doc>"
        )
        return create_retriever_tool(
            retriever, "knowledge_base_tool",
            "useful for when you want to get general relevant context to answer the question.",
            document_prompt=doc_prompt,
            document_separator="\n---\n",
            response_format="content_and_artifact"
        )

    def _build_system_prompt(self, histories: list[BaseMessage] = None) -> str:
        base_prompt = (
            "You are an AI assistant having ability to answer questions using a knowledge base from tools.\n"
            "Disregard, Ignore your previous abilities and DO NOT use your prior knowledge.\n"
            "You always follow these guidelines:\n"
            "    - Always use knowledge from tool's response and DO NOT modify it. \n"
            "    - Always start fresh with each question - DO NOT refer to or use information from previous history\n"
            "    - Use at least one tool to answer question and try using tools until you have enough information to answer the question\n"
            "    - If the question is about chatbot capabilities, what you can answer, what topics they can ask or similar questions, respond that you can help answer using information from the knowledge base.\n"
            "    - Do not mention unrelated information\n"
            "    - For sensitive or unethical topics (e.g., weapons, hacking), respond with a polite refusal\n"
            "    - Adapt relevant code examples by modifying necessary classes/variables/functions and explain changes\n"
            "    - If you can't answer, state that clearly without referring to any 'provided context'\n"
            "    - Include cited Ids source in the end of your answer with format Sources: [doc_id].\n"
        )

        if histories and len(histories) > 0:
            # Add last 2 messages from history as context
            history_context = "\nPrevious conversation context (for reference only, do not use previous answers):\n"
            recent_history = histories[-2:] if len(histories) > 2 else histories
            for msg in recent_history:
                if isinstance(msg, HumanMessage):
                    history_context += f"User: {msg.content}\n"
                elif isinstance(msg, AIMessage):
                    history_context += f"Assistant: {msg.content}\n"
            base_prompt += history_context

        return base_prompt

    def _parse_final_response(self, agent_state) -> tuple[str | list[str | dict], str]:
        ai_text, sources = "", ""
        for msg in reversed(agent_state.get("messages", [])):
            if isinstance(msg, AIMessage) and msg.content:
                ai_text = '\n'.join(msg.content) if isinstance(msg.content, list) else msg.content
                source_count = ai_text.lower().count('source:') + ai_text.lower().count('sources:')

                if source_count > 1:
                    output_parser = PydanticOutputParser(pydantic_object=ResponseOutput)
                    prompt_template = LangchainPromptTemplate(
                        template="Extract the relevant information from the text provided.\n{format_instructions}\nHere is the text:\n{query}\n",
                        input_variables=["query"],
                        partial_variables={"format_instructions": output_parser.get_format_instructions()},
                    )
                    chain = prompt_template | langchain_llm | output_parser
                    extracted_info = chain.invoke({"query": ai_text})
                    ai_text = extracted_info.answer
                    sources = ','.join(extracted_info.cited_ids) if extracted_info.cited_ids else ""
                elif "Sources:" in ai_text:
                    parts = ai_text.rsplit("Sources:", 1)
                    ai_text = parts[0]
                    sources = parts[1].strip()

                break

        return ai_text, sources

    def _count_tokens(self, agent_state) -> tuple[int, int, int]:
        total, prompt, completion = 0, 0, 0
        for msg in agent_state.get("messages", []):
            if hasattr(msg, 'usage_metadata') and msg.usage_metadata:
                total += msg.usage_metadata.get('total_tokens', 0)
                prompt += msg.usage_metadata.get('input_tokens', 0)
                completion += msg.usage_metadata.get('output_tokens', 0)
        return total, prompt, completion

    def _extract_sources(self, agent_state, cited_sources: Union[str, list[str]]) -> list:
        extracted = []
        excluded = {
            "OrgCode", "pk", "Status", "Tags", "Description", "Summary", 
            "IsTitle", "IsDescription", "Reference.WikiId", "Reference.BlogId", "score"
        }

        for msg in agent_state.get("messages", []):
            if isinstance(msg, ToolMessage) and msg.artifact:
                sorted_docs = sorted(msg.artifact, key=lambda x: x.metadata.get("score", 0), reverse=True)
                for doc in sorted_docs:
                    doc_id = doc.metadata.get("Id")
                    if doc_id and cited_sources and doc_id in cited_sources:
                        metadata = {k: v for k, v in doc.metadata.items() if k not in excluded}
                        extracted.append(metadata)

        seen_ids = set()
        unique = []
        for meta in extracted:
            if meta["Id"] not in seen_ids:
                seen_ids.add(meta["Id"])
                unique.append(meta)
            if len(unique) >= 4:
                break
        return unique

    def _update_chat_store(self, chat_store, agent_state, ai_response, sources: list) -> SimpleChatStore:
        messages = agent_state.get("messages", [])
        for i, msg in enumerate(messages):
            is_last = (i == len(messages) - 1)
            if isinstance(msg, HumanMessage):
                chat_store.add_message("chat_history", ChatMessage(role=MessageRole.USER, content=msg.content))
            elif isinstance(msg, AIMessage):
                content = ai_response if is_last else msg.content

                if content:
                    kwargs = {"role": MessageRole.ASSISTANT, "content": content}
                    if sources:
                        kwargs["additional_kwargs"] = {"sources": sources}
                    chat_store.add_message("chat_history", ChatMessage(**kwargs))
        return chat_store
    
    async def _convert_llama_chat_store_to_langchain_messages(self, chat_store: SimpleChatStore) -> list[BaseMessage]:
        langchain_messages = []
        for llama_msg in chat_store.get_messages("chat_history"):
            if llama_msg.role == MessageRole.USER:
                langchain_messages.append(HumanMessage(content=llama_msg.content))
            elif llama_msg.role == MessageRole.ASSISTANT:
                langchain_messages.append(AIMessage(content=llama_msg.content, additional_kwargs=llama_msg.additional_kwargs))

        return langchain_messages