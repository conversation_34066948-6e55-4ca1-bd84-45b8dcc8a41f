#!/usr/bin/env python3
"""
Test the complete YouTube transcript solution.
"""

import os
import logging
from youtube_api_helper import get_youtube_transcript_reliable, configure_youtube_api_key

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_youtube_solution():
    """Test the YouTube transcript solution with API fallback."""
    
    # Configure API key if available
    youtube_api_key = os.getenv("YOUTUBE_API_KEY", "AIzaSyBDqW_YI17HGJxZc0mZ0vy-PmHdkhOnapw")
    if youtube_api_key:
        configure_youtube_api_key(youtube_api_key)
        print("✅ YouTube Data API v3 configured")
    else:
        print("⚠️  No YouTube API key found - using fallback method")
    
    # Test video ID (<PERSON> Roll - known to have transcripts)
    video_id = "dQw4w9WgXcQ"
    
    print(f"\n=== Testing YouTube Transcript Solution ===")
    print(f"Video ID: {video_id}")
    print("This solution includes:")
    print("- YouTube Data API v3 for reliable access")
    print("- Single-attempt transcript fetch to avoid bot detection")
    print("- Comprehensive error handling")
    
    try:
        print("\nAttempting transcript extraction...")
        transcript = get_youtube_transcript_reliable(video_id)
        
        if transcript and len(transcript) > 50:
            print("🎉 SUCCESS: Transcript extracted successfully!")
            print(f"   Length: {len(transcript)} characters")
            
            # Show sample
            lines = transcript.split('\n')
            print(f"   Lines: {len(lines)}")
            print("   Sample content:")
            for i, line in enumerate(lines[:3]):
                print(f"     {line}")
            if len(lines) > 3:
                print("     ...")
            
            return True
        else:
            print("❌ FAILED: Empty or very short transcript")
            return False
            
    except Exception as e:
        error_msg = str(e)
        print(f"❌ FAILED: {error_msg}")
        
        # Provide specific guidance based on error
        if "bot detection" in error_msg.lower():
            print("\n💡 Bot Detection Solutions:")
            print("1. Wait 15-30 minutes and try again")
            print("2. Use a different network/IP address")
            print("3. The YouTube Data API v3 key should help avoid this")
            print("4. Try accessing the video in browser first")
        elif "API key" in error_msg:
            print("\n💡 API Key Solutions:")
            print("1. Get YouTube Data API v3 key from Google Cloud Console")
            print("2. Enable YouTube Data API v3 in your project")
            print("3. Set YOUTUBE_API_KEY environment variable")
        elif "TranscriptsDisabled" in error_msg:
            print("\n💡 This video doesn't have transcripts enabled")
        
        return False

def test_with_your_video():
    """Test with a specific video URL."""
    video_url = input("\nEnter a YouTube URL to test (or press Enter to skip): ").strip()
    
    if not video_url:
        return
    
    # Extract video ID
    import re
    patterns = [
        r"^https?://(?:www\.)?youtube\.com/watch\?v=([\w-]+)",
        r"^https?://(?:www\.)?youtube\.com/embed/([\w-]+)",
        r"^https?://youtu\.be/([\w-]+)",
    ]
    
    video_id = None
    for pattern in patterns:
        match = re.search(pattern, video_url)
        if match:
            video_id = match.group(1)
            break
    
    if not video_id:
        print("❌ Could not extract video ID from URL")
        return
    
    print(f"\nTesting with your video: {video_id}")
    
    try:
        transcript = get_youtube_transcript_reliable(video_id)
        
        if transcript and len(transcript) > 50:
            print("✅ SUCCESS: Your video transcript extracted!")
            print(f"   Length: {len(transcript)} characters")
        else:
            print("❌ No transcript content for your video")
            
    except Exception as e:
        print(f"❌ Failed for your video: {str(e)}")

if __name__ == "__main__":
    print("YouTube Transcript Solution Test")
    print("=" * 50)
    
    # Test 1: Standard test
    success = test_youtube_solution()
    
    # Test 2: User's video
    if success:
        test_with_your_video()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Solution is working! Your application should now handle YouTube videos better.")
        print("\nNext steps:")
        print("1. Try your application with YouTube URLs")
        print("2. Monitor the logs for any remaining issues")
        print("3. If problems persist, wait 15-30 minutes between attempts")
    else:
        print("⚠️  Solution needs attention. See error messages above for guidance.")
        print("\nImmediate actions:")
        print("1. Wait 15-30 minutes for bot detection to clear")
        print("2. Ensure YouTube Data API v3 is properly configured")
        print("3. Try from a different network if possible")
